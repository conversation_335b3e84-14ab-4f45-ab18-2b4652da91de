using System;
using System.Collections.Generic;
using System.Linq;

namespace TeklaTool.Utils
{
    /// <summary>
    /// Tekla Structures版本兼容性管理器
    /// 处理不同版本之间的API差异和兼容性问题
    /// </summary>
    public static class TeklaVersionCompatibility
    {
        /// <summary>
        /// 支持的Tekla版本信息
        /// </summary>
        public static readonly Dictionary<string, TeklaVersionInfo> SupportedVersions = new Dictionary<string, TeklaVersionInfo>
        {
            { "19.0", new TeklaVersionInfo("19.0", "Tekla Structures 19.0", new Version(19, 0), true) },
            { "19.1", new TeklaVersionInfo("19.1", "Tekla Structures 19.1", new Version(19, 1), true) },
            { "20.0", new TeklaVersionInfo("20.0", "Tekla Structures 20.0", new Version(20, 0), true) },
            { "20.1", new TeklaVersionInfo("20.1", "Tekla Structures 20.1", new Version(20, 1), true) },
            { "21.0", new TeklaVersionInfo("21.0", "Tekla Structures 21.0", new Version(21, 0), true) },
            { "21.1", new TeklaVersionInfo("21.1", "Tekla Structures 21.1", new Version(21, 1), true) },
            { "2019.0", new TeklaVersionInfo("2019.0", "Tekla Structures 2019", new Version(2019, 0), true) },
            { "2019.1", new TeklaVersionInfo("2019.1", "Tekla Structures 2019 SP1", new Version(2019, 1), true) },
            { "2020.0", new TeklaVersionInfo("2020.0", "Tekla Structures 2020", new Version(2020, 0), true) },
            { "2020.1", new TeklaVersionInfo("2020.1", "Tekla Structures 2020 SP1", new Version(2020, 1), true) },
            { "2021.0", new TeklaVersionInfo("2021.0", "Tekla Structures 2021", new Version(2021, 0), true) },
            { "2021.1", new TeklaVersionInfo("2021.1", "Tekla Structures 2021 SP1", new Version(2021, 1), true) },
            { "2022.0", new TeklaVersionInfo("2022.0", "Tekla Structures 2022", new Version(2022, 0), true) },
            { "2023.0", new TeklaVersionInfo("2023.0", "Tekla Structures 2023", new Version(2023, 0), true) }
        };

        /// <summary>
        /// 检查指定版本是否受支持
        /// </summary>
        /// <param name="version">版本字符串</param>
        /// <returns>是否受支持</returns>
        public static bool IsVersionSupported(string version)
        {
            if (string.IsNullOrEmpty(version)) return false;
            
            // 直接匹配
            if (SupportedVersions.ContainsKey(version))
            {
                return SupportedVersions[version].IsSupported;
            }

            // 尝试模糊匹配
            var normalizedVersion = NormalizeVersion(version);
            return SupportedVersions.Values.Any(v => 
                NormalizeVersion(v.VersionString) == normalizedVersion && v.IsSupported);
        }

        /// <summary>
        /// 获取版本信息
        /// </summary>
        /// <param name="version">版本字符串</param>
        /// <returns>版本信息，如果不支持则返回null</returns>
        public static TeklaVersionInfo GetVersionInfo(string version)
        {
            if (string.IsNullOrEmpty(version)) return null;

            // 直接匹配
            if (SupportedVersions.ContainsKey(version))
            {
                return SupportedVersions[version];
            }

            // 尝试模糊匹配
            var normalizedVersion = NormalizeVersion(version);
            return SupportedVersions.Values.FirstOrDefault(v => 
                NormalizeVersion(v.VersionString) == normalizedVersion);
        }

        /// <summary>
        /// 获取兼容性警告信息
        /// </summary>
        /// <param name="version">版本字符串</param>
        /// <returns>警告信息列表</returns>
        public static List<string> GetCompatibilityWarnings(string version)
        {
            var warnings = new List<string>();
            var versionInfo = GetVersionInfo(version);

            if (versionInfo == null)
            {
                warnings.Add($"未知的Tekla版本: {version}，可能存在兼容性问题");
                return warnings;
            }

            // 检查版本特定的兼容性问题
            if (versionInfo.Version.Major < 19)
            {
                warnings.Add("检测到较旧的Tekla版本，某些功能可能不可用");
            }

            if (versionInfo.Version.Major >= 2022)
            {
                warnings.Add("检测到较新的Tekla版本，如果遇到问题请联系开发者更新程序");
            }

            // 检查已知的API变更
            CheckApiChanges(versionInfo, warnings);

            return warnings;
        }

        /// <summary>
        /// 检查API变更
        /// </summary>
        private static void CheckApiChanges(TeklaVersionInfo versionInfo, List<string> warnings)
        {
            // 这里可以添加已知的API变更检查
            // 例如：某些方法在特定版本中被弃用或更改

            if (versionInfo.Version.Major >= 21)
            {
                // 21.0版本开始的一些API变更
                // 目前没有发现影响本程序的重大变更
            }

            if (versionInfo.Version.Major >= 2020)
            {
                // 2020版本开始的一些变更
                // 目前没有发现影响本程序的重大变更
            }
        }

        /// <summary>
        /// 标准化版本字符串
        /// </summary>
        private static string NormalizeVersion(string version)
        {
            if (string.IsNullOrEmpty(version)) return string.Empty;

            // 移除空格和特殊字符
            version = version.Trim().Replace(" ", "");

            // 确保格式一致性
            if (Version.TryParse(version, out var parsedVersion))
            {
                return $"{parsedVersion.Major}.{parsedVersion.Minor}";
            }

            return version;
        }

        /// <summary>
        /// 获取推荐的最低版本
        /// </summary>
        public static string GetRecommendedMinimumVersion()
        {
            return "19.0";
        }

        /// <summary>
        /// 获取所有支持的版本列表
        /// </summary>
        public static List<string> GetSupportedVersionsList()
        {
            return SupportedVersions.Values
                .Where(v => v.IsSupported)
                .OrderBy(v => v.Version)
                .Select(v => v.DisplayName)
                .ToList();
        }
    }

    /// <summary>
    /// Tekla版本信息
    /// </summary>
    public class TeklaVersionInfo
    {
        public string VersionString { get; }
        public string DisplayName { get; }
        public Version Version { get; }
        public bool IsSupported { get; }

        public TeklaVersionInfo(string versionString, string displayName, Version version, bool isSupported)
        {
            VersionString = versionString;
            DisplayName = displayName;
            Version = version;
            IsSupported = isSupported;
        }

        public override string ToString()
        {
            return DisplayName;
        }
    }
}
