{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{AFADC28E-2861-45C3-8D65-535FE9696F37}|TeklaListWPF.csproj|c:\\users\\<USER>\\desktop\\teklalistwpf\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{AFADC28E-2861-45C3-8D65-535FE9696F37}|TeklaListWPF.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{AFADC28E-2861-45C3-8D65-535FE9696F37}|TeklaListWPF.csproj|c:\\users\\<USER>\\desktop\\teklalistwpf\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{AFADC28E-2861-45C3-8D65-535FE9696F37}|TeklaListWPF.csproj|solutionrelative:app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{AFADC28E-2861-45C3-8D65-535FE9696F37}|TeklaListWPF.csproj|c:\\users\\<USER>\\desktop\\teklalistwpf\\services\\teklamodelservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AFADC28E-2861-45C3-8D65-535FE9696F37}|TeklaListWPF.csproj|solutionrelative:services\\teklamodelservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AFADC28E-2861-45C3-8D65-535FE9696F37}|TeklaListWPF.csproj|c:\\users\\<USER>\\desktop\\teklalistwpf\\models\\modelconnection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AFADC28E-2861-45C3-8D65-535FE9696F37}|TeklaListWPF.csproj|solutionrelative:models\\modelconnection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{57d563b6-44a5-47df-85be-f4199ad6b651}"}, {"$type": "Bookmark", "Name": "ST:0:0:{269a02dc-6af8-11d3-bdc4-00c04f688e50}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e5c86464-96be-4d7c-9a8b-abcb3bbf5f92}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "App.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\App.xaml", "RelativeDocumentMoniker": "App.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\App.xaml", "RelativeToolTip": "App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-27T11:23:10.988Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-15T09:09:53.698Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "TeklaModelService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\Services\\TeklaModelService.cs", "RelativeDocumentMoniker": "Services\\TeklaModelService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\Services\\TeklaModelService.cs", "RelativeToolTip": "Services\\TeklaModelService.cs", "ViewState": "AgIAAGEBAAAAAAAAAAAkwH0BAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T08:07:38.322Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ModelConnection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\Models\\ModelConnection.cs", "RelativeDocumentMoniker": "Models\\ModelConnection.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\Models\\ModelConnection.cs", "RelativeToolTip": "Models\\ModelConnection.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T08:07:35.187Z"}]}, {"DockedWidth": 258, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{0c720079-b2d5-4f29-840e-382783199460}"}]}, {"DockedWidth": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}]}]}]}