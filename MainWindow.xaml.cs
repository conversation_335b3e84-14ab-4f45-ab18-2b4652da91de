﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using TeklaTool.Models;
using TeklaTool.ViewModels;
using TeklaTool.Views;
using TeklaTool.Utils;

namespace TeklaTool
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private MainViewModel _viewModel;

        public MainWindow()
        {
            InitializeComponent();
            _viewModel = (MainViewModel)DataContext;

            // 初始化零件数据网格列
            InitializePartsDataGrid();

            // 初始化构件数据网格列
            InitializeAssembliesDataGrid();

            // 注册窗口加载事件
            Loaded += MainWindow_Loaded;
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("========== MainWindow_Loaded 开始 ==========");

                // 窗口加载完成后的初始化操作
                Title = $"TeklaList - {System.Reflection.Assembly.GetExecutingAssembly().GetName().Version}";
                System.Diagnostics.Debug.WriteLine($"设置窗口标题: {Title}");

                // 设置默认排序
                SetDefaultSorting();
                System.Diagnostics.Debug.WriteLine("已设置默认排序");

                // 监听视图模式变更，以便在切换模式时应用正确的排序
                _viewModel.PropertyChanged += ViewModel_PropertyChanged;
                System.Diagnostics.Debug.WriteLine("已添加视图模式变更事件处理");

                // 不再需要设置测试数据
                System.Diagnostics.Debug.WriteLine("使用新的筛选控件，不需要设置测试数据");

                System.Diagnostics.Debug.WriteLine("========== MainWindow_Loaded 结束 ==========");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"窗口加载时出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
            }
        }

        // 清除所有筛选按钮点击事件
        private void ClearAllFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[MainWindow] 清除所有筛选按钮点击");

                // 根据当前显示的数据网格，调用相应的清除筛选方法
                if (_viewModel.IsAssemblyMode)
                {
                    // 构件模式
                    var assembliesGrid = (Views.FilterableDataGrid)AssembliesDataGrid2;
                    assembliesGrid.ClearFilters();
                }
                else
                {
                    // 零件模式
                    var partsGrid = (Views.FilterableDataGrid)PartsDataGrid2;
                    partsGrid.ClearFilters();
                }

                System.Diagnostics.Debug.WriteLine("[MainWindow] 清除所有筛选完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[MainWindow] 清除所有筛选时出错: {ex.Message}");
                MessageBox.Show($"清除筛选时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 刷新数据按钮点击事件（用于诊断）
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[MainWindow] 刷新数据按钮被点击");
                MessageBox.Show("刷新数据按钮被点击！\n\n这证明按钮可以响应点击事件。\n如果命令没有执行，说明命令绑定有问题。", "调试信息", MessageBoxButton.OK, MessageBoxImage.Information);

                // 检查DataContext和命令
                if (DataContext is MainViewModel vm)
                {
                    System.Diagnostics.Debug.WriteLine($"[MainWindow] DataContext类型正确: {vm.GetType().Name}");
                    System.Diagnostics.Debug.WriteLine($"[MainWindow] RefreshDataCommand是否为null: {vm.RefreshDataCommand == null}");

                    if (vm.RefreshDataCommand != null)
                    {
                        bool canExecute = vm.RefreshDataCommand.CanExecute(null);
                        System.Diagnostics.Debug.WriteLine($"[MainWindow] RefreshDataCommand.CanExecute: {canExecute}");
                        MessageBox.Show($"命令状态检查：\n- 命令存在: {vm.RefreshDataCommand != null}\n- 可以执行: {canExecute}\n- IsLoading: {vm.IsLoading}", "命令状态", MessageBoxButton.OK, MessageBoxImage.Information);

                        if (canExecute)
                        {
                            System.Diagnostics.Debug.WriteLine("[MainWindow] 手动执行RefreshDataCommand");
                            vm.RefreshDataCommand.Execute(null);
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[MainWindow] DataContext类型错误: {DataContext?.GetType().Name ?? "null"}");
                    MessageBox.Show($"DataContext问题：\n类型: {DataContext?.GetType().Name ?? "null"}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[MainWindow] 刷新按钮点击处理出错: {ex.Message}");
                MessageBox.Show($"处理刷新按钮点击时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializePartsDataGrid()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("========== 初始化零件数据网格 ==========");

                // 添加零件数据网格的列
                var partsGrid = (Views.FilterableDataGrid)PartsDataGrid2;
                System.Diagnostics.Debug.WriteLine("获取到零件数据网格控件");

                // 添加列
                System.Diagnostics.Debug.WriteLine("开始添加列...");
                partsGrid.AddColumn("序号", "Index");
                partsGrid.AddColumn("构件编号", "AssemblyNumber");
                partsGrid.AddColumn("零件编号", "PartNumber");
                partsGrid.AddColumn("数量", "Count");
                partsGrid.AddColumn("名称", "Name");
                partsGrid.AddColumn("截面", "Profile");
                partsGrid.AddColumn("材质", "Material");
                partsGrid.AddColumn("表面处理", "Finish");
                partsGrid.AddColumn("等级", "Class");
                partsGrid.AddColumn("阶段", "Phase");
                partsGrid.AddColumn("螺栓数", "BoltCount");
                partsGrid.AddColumn("主/次", "MainPartDisplay");
                partsGrid.AddColumn("构件前缀", "AssemblyPrefix");
                partsGrid.AddColumn("构件起始号", "AssemblyStartNumber");
                partsGrid.AddColumn("零件前缀", "PartPrefix");
                partsGrid.AddColumn("零件起始号", "PartStartNumber");
                partsGrid.AddColumn("GUID", "Guid");
                partsGrid.AddColumn("备注", "Remark");
                System.Diagnostics.Debug.WriteLine($"已添加 {partsGrid.MainDataGrid.Columns.Count} 列");

                // 添加选择变更事件处理
                partsGrid.MainDataGrid.SelectionChanged += PartsDataGrid_SelectionChanged;
                System.Diagnostics.Debug.WriteLine("已添加选择变更事件处理");

                System.Diagnostics.Debug.WriteLine("========== 零件数据网格初始化完成 ==========");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化零件数据网格时出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                MessageBox.Show($"初始化零件数据网格时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeAssembliesDataGrid()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("========== 初始化构件数据网格 ==========");

                // 添加构件数据网格的列
                var assembliesGrid = (Views.FilterableDataGrid)AssembliesDataGrid2;
                System.Diagnostics.Debug.WriteLine("获取到构件数据网格控件");

                // 添加列
                System.Diagnostics.Debug.WriteLine("开始添加列...");
                assembliesGrid.AddColumn("序号", "Index");
                assembliesGrid.AddColumn("构件编号", "AssemblyNumber");
                assembliesGrid.AddColumn("数量", "Count");
                assembliesGrid.AddColumn("名称", "Name");
                assembliesGrid.AddColumn("截面", "Profile");
                assembliesGrid.AddColumn("材质", "Material");
                assembliesGrid.AddColumn("表面处理", "Finish");
                assembliesGrid.AddColumn("等级", "Class");
                assembliesGrid.AddColumn("阶段", "Phase");
                assembliesGrid.AddColumn("零件数", "PartCount");
                assembliesGrid.AddColumn("构件前缀", "AssemblyPrefix");
                assembliesGrid.AddColumn("构件起始号", "AssemblyStartNumber");
                assembliesGrid.AddColumn("GUID", "Guid");
                assembliesGrid.AddColumn("备注", "Remark");
                System.Diagnostics.Debug.WriteLine($"已添加 {assembliesGrid.MainDataGrid.Columns.Count} 列");

                // 添加选择变更事件处理
                assembliesGrid.MainDataGrid.SelectionChanged += AssembliesDataGrid_SelectionChanged;
                System.Diagnostics.Debug.WriteLine("已添加选择变更事件处理");

                System.Diagnostics.Debug.WriteLine("========== 构件数据网格初始化完成 ==========");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化构件数据网格时出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                MessageBox.Show($"初始化构件数据网格时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            // 当视图模式变更时，重新应用默认排序
            if (e.PropertyName == nameof(_viewModel.IsAssemblyMode))
            {
                SetDefaultSorting();
            }
        }

        /// <summary>
        /// 设置默认排序
        /// </summary>
        private void SetDefaultSorting()
        {
            try
            {
                // 根据当前模式设置默认排序
                if (_viewModel.IsAssemblyMode)
                {
                    // 构件模式：按构件编号排序
                    var assembliesGrid = (Views.FilterableDataGrid)AssembliesDataGrid2;
                    assembliesGrid.SetDefaultSort("AssemblyNumber", ListSortDirection.Ascending);
                }
                else
                {
                    // 零件模式：按零件编号排序
                    var partsGrid = (Views.FilterableDataGrid)PartsDataGrid2;
                    partsGrid.SetDefaultSort("PartNumber", ListSortDirection.Ascending);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置默认排序时出错: {ex.Message}");
            }
        }

        // 防抖动计时器
        private System.Timers.Timer _partsSelectionDebounceTimer;
        private System.Timers.Timer _assembliesSelectionDebounceTimer;
        private object _partsSelectionLock = new object();
        private object _assembliesSelectionLock = new object();

        private void PartsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is DataGrid dataGrid)
            {
                lock (_partsSelectionLock)
                {
                    // 如果计时器已存在，重置它
                    if (_partsSelectionDebounceTimer != null)
                    {
                        _partsSelectionDebounceTimer.Stop();
                        _partsSelectionDebounceTimer.Dispose();
                    }

                    // 创建新的计时器，延迟300毫秒执行
                    _partsSelectionDebounceTimer = new System.Timers.Timer(300);
                    _partsSelectionDebounceTimer.AutoReset = false;
                    _partsSelectionDebounceTimer.Elapsed += (s, args) =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                if (_viewModel.IsMergeRows)
                                {
                                    try
                                    {
                                        // 合并模式下，选中MergedPartRow，需找到所有对应TeklaModelPart
                                        var selectedItems = new List<TeklaModelPart>();

                                        // 检查每个选中项的类型
                                        foreach (var item in dataGrid.SelectedItems)
                                        {
                                            if (item is TeklaTool.ViewModels.PartListViewModel.MergedPartRow mergedRow)
                                            {
                                                // 找到所有具有相同零件编号的零件
                                                var matchingParts = _viewModel.Parts.Where(p => p.PartNumber == mergedRow.PartNumber).ToList();
                                                selectedItems.AddRange(matchingParts);
                                            }
                                            else if (item is TeklaModelPart part)
                                            {
                                                // 如果直接是零件，也添加到列表中
                                                selectedItems.Add(part);
                                            }
                                        }

                                        if (selectedItems.Count > 0)
                                        {
                                            _viewModel.PartList.HandlePartSelectionChanged(selectedItems);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        MessageBox.Show($"处理合并行选择时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                    }
                                }
                                else
                                {
                                    try
                                    {
                                        var selectedItems = dataGrid.SelectedItems.Cast<TeklaModelPart>().ToList();
                                        _viewModel.PartList.HandlePartSelectionChanged(selectedItems);
                                    }
                                    catch (Exception ex)
                                    {
                                        MessageBox.Show($"处理选择时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"处理选择变更时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        });
                    };

                    _partsSelectionDebounceTimer.Start();
                }
            }
        }

        private void AssembliesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is DataGrid dataGrid)
            {
                lock (_assembliesSelectionLock)
                {
                    // 如果计时器已存在，重置它
                    if (_assembliesSelectionDebounceTimer != null)
                    {
                        _assembliesSelectionDebounceTimer.Stop();
                        _assembliesSelectionDebounceTimer.Dispose();
                    }

                    // 创建新的计时器，延迟300毫秒执行
                    _assembliesSelectionDebounceTimer = new System.Timers.Timer(300);
                    _assembliesSelectionDebounceTimer.AutoReset = false;
                    _assembliesSelectionDebounceTimer.Elapsed += (s, args) =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                if (_viewModel.IsMergeRows)
                                {
                                    try
                                    {
                                        // 合并模式下，直接处理MergedAssemblyRow
                                        var selectedMergedRows = new List<TeklaTool.ViewModels.AssemblyListViewModel.MergedAssemblyRow>();

                                        // 检查每个选中项的类型
                                        foreach (var item in dataGrid.SelectedItems)
                                        {
                                            if (item is TeklaTool.ViewModels.AssemblyListViewModel.MergedAssemblyRow mergedRow)
                                            {
                                                selectedMergedRows.Add(mergedRow);
                                            }
                                        }

                                        // 使用专门的合并行选择处理方法
                                        _viewModel.AssemblyList.HandleMergedAssemblySelectionChanged(selectedMergedRows);
                                    }
                                    catch (Exception ex)
                                    {
                                        MessageBox.Show($"处理合并行选择时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                    }
                                }
                                else
                                {
                                    try
                                    {
                                        var selectedItems = dataGrid.SelectedItems.Cast<TeklaTool.Models.AssemblyInfo>().ToList();
                                        _viewModel.AssemblyList.HandleAssemblySelectionChanged(selectedItems);
                                    }
                                    catch (Exception ex)
                                    {
                                        MessageBox.Show($"处理选择时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"处理选择变更时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        });
                    };

                    _assembliesSelectionDebounceTimer.Start();
                }
            }
        }

        #region 菜单事件处理

        /// <summary>
        /// Tekla版本信息菜单项点击事件
        /// </summary>
        private void TeklaVersionInfoMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var versionWindow = new TeklaVersionInfoWindow
                {
                    Owner = this
                };
                versionWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                Logger.LogError($"打开Tekla版本信息窗口时发生错误: {ex.Message}");
                MessageBox.Show($"打开版本信息窗口时发生错误:\n{ex.Message}",
                               "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 清除缓存菜单项点击事件
        /// </summary>
        private void ClearCacheMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "确定要清除所有缓存吗？\n\n这将清除：\n- Tekla程序集缓存\n- 零件数据缓存\n- 构件数据缓存\n\n清除后需要重新加载数据。",
                    "确认清除缓存",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // 清除Tekla程序集缓存
                    TeklaAssemblyLoader.ClearCache();

                    // 清除数据缓存
                    _viewModel?.ClearAllCache();

                    Logger.LogInfo("已清除所有缓存");
                    MessageBox.Show("缓存已清除完成！", "操作完成",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"清除缓存时发生错误: {ex.Message}");
                MessageBox.Show($"清除缓存时发生错误:\n{ex.Message}",
                               "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 关于菜单项点击事件
        /// </summary>
        private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version;
                var teklaVersion = TeklaAssemblyLoader.DetectedTeklaVersion ?? "未检测到";

                var aboutText = $"TeklaListWPF\n\n" +
                               $"版本: {version}\n" +
                               $"Tekla版本: {teklaVersion}\n\n" +
                               $"这是一个用于显示和管理Tekla Structures模型中零件和构件信息的工具。\n\n" +
                               $"主要功能：\n" +
                               $"• 显示零件和构件列表\n" +
                               $"• 支持多种筛选和排序\n" +
                               $"• 高亮显示选中的模型对象\n" +
                               $"• 自动适配不同Tekla版本\n\n" +
                               $"开发者: Augment Agent\n" +
                               $"基于: .NET Framework 4.8";

                MessageBox.Show(aboutText, "关于 TeklaListWPF",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Logger.LogError($"显示关于信息时发生错误: {ex.Message}");
                MessageBox.Show($"显示关于信息时发生错误:\n{ex.Message}",
                               "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 退出菜单项点击事件
        /// </summary>
        private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        #endregion
    }
}