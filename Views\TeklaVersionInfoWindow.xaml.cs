using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Windows;
using TeklaTool.Utils;

namespace TeklaTool.Views
{
    /// <summary>
    /// TeklaVersionInfoWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TeklaVersionInfoWindow : Window
    {
        private TeklaConfig _config;

        public TeklaVersionInfoWindow()
        {
            InitializeComponent();
            _config = TeklaConfig.Instance;
            LoadData();
        }

        /// <summary>
        /// 加载数据到界面
        /// </summary>
        private void LoadData()
        {
            try
            {
                // 加载版本信息
                LoadVersionInfo();

                // 加载程序集信息
                LoadAssemblyInfo();

                // 加载兼容性警告
                LoadCompatibilityWarnings();

                // 加载配置信息
                LoadConfigInfo();

                // 加载支持的版本列表
                LoadSupportedVersions();

                StatusText.Text = "数据加载完成";
            }
            catch (Exception ex)
            {
                Logger.LogError($"加载版本信息窗口数据时发生错误: {ex.Message}");
                StatusText.Text = $"加载失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 加载版本信息
        /// </summary>
        private void LoadVersionInfo()
        {
            var detectedVersion = TeklaAssemblyLoader.DetectedTeklaVersion;
            CurrentVersionText.Text = string.IsNullOrEmpty(detectedVersion) ? "未检测到" : detectedVersion;

            if (!string.IsNullOrEmpty(detectedVersion))
            {
                var isSupported = TeklaVersionCompatibility.IsVersionSupported(detectedVersion);
                CompatibilityStatusText.Text = isSupported ? "✓ 完全兼容" : "⚠ 可能存在兼容性问题";
                CompatibilityStatusText.Foreground = isSupported ?
                    System.Windows.Media.Brushes.Green :
                    System.Windows.Media.Brushes.Orange;
            }
            else
            {
                CompatibilityStatusText.Text = "❌ 无法确定";
                CompatibilityStatusText.Foreground = System.Windows.Media.Brushes.Red;
            }

            DetectionTimeText.Text = _config.LastDetectionTime == DateTime.MinValue ?
                "从未检测" :
                _config.LastDetectionTime.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 加载程序集信息
        /// </summary>
        private void LoadAssemblyInfo()
        {
            var assemblies = new List<TeklaAssemblyInfo>();

            var coreAssemblyNames = new[]
            {
                "Tekla.Structures",
                "Tekla.Structures.Model",
                "Tekla.Structures.Drawing"
            };

            foreach (var assemblyName in coreAssemblyNames)
            {
                var assembly = TeklaAssemblyLoader.GetLoadedAssembly(assemblyName);
                if (assembly != null)
                {
                    assemblies.Add(new TeklaAssemblyInfo
                    {
                        Name = assemblyName,
                        Version = assembly.GetName().Version?.ToString() ?? "未知",
                        Status = "已加载",
                        Location = assembly.GlobalAssemblyCache ? "GAC" : assembly.Location
                    });
                }
                else
                {
                    assemblies.Add(new TeklaAssemblyInfo
                    {
                        Name = assemblyName,
                        Version = "N/A",
                        Status = "未加载",
                        Location = "N/A"
                    });
                }
            }

            AssembliesDataGrid.ItemsSource = assemblies;
        }

        /// <summary>
        /// 加载兼容性警告
        /// </summary>
        private void LoadCompatibilityWarnings()
        {
            var detectedVersion = TeklaAssemblyLoader.DetectedTeklaVersion;
            var warnings = new List<string>();

            if (!string.IsNullOrEmpty(detectedVersion))
            {
                warnings = TeklaVersionCompatibility.GetCompatibilityWarnings(detectedVersion);
            }

            if (warnings.Count == 0)
            {
                warnings.Add("没有发现兼容性问题");
            }

            WarningsListBox.ItemsSource = warnings;
            WarningsGroupBox.Visibility = warnings.Count > 0 ? Visibility.Visible : Visibility.Collapsed;
        }

        /// <summary>
        /// 加载配置信息
        /// </summary>
        private void LoadConfigInfo()
        {
            // 加载首选版本下拉框
            var supportedVersions = TeklaVersionCompatibility.SupportedVersions.Keys.ToList();
            supportedVersions.Insert(0, ""); // 添加空选项表示自动检测
            PreferredVersionComboBox.ItemsSource = supportedVersions;
            PreferredVersionComboBox.SelectedItem = _config.PreferredTeklaVersion;

            // 加载复选框状态
            EnableCompatibilityCheckBox.IsChecked = _config.EnableCompatibilityCheck;
            ShowVersionWarningsCheckBox.IsChecked = _config.ShowVersionWarnings;
            EnableAssemblyCacheCheckBox.IsChecked = _config.EnableAssemblyCache;
            ForceGacLoadingCheckBox.IsChecked = _config.ForceGacLoading;

            // 加载数值设置
            TimeoutTextBox.Text = _config.AssemblyLoadTimeoutSeconds.ToString();
            CacheHoursTextBox.Text = _config.DetectionCacheHours.ToString();
        }

        /// <summary>
        /// 加载支持的版本列表
        /// </summary>
        private void LoadSupportedVersions()
        {
            var supportedVersions = TeklaVersionCompatibility.GetSupportedVersionsList();
            SupportedVersionsListBox.ItemsSource = supportedVersions;
        }

        /// <summary>
        /// 重新检测版本按钮点击事件
        /// </summary>
        private void RefreshVersionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusText.Text = "正在重新检测版本...";
                RefreshVersionButton.IsEnabled = false;

                // 清除缓存并重新初始化
                TeklaAssemblyLoader.ClearCache();
                _config.LastDetectedVersion = string.Empty;
                _config.LastDetectionTime = DateTime.MinValue;

                if (TeklaAssemblyLoader.Initialize())
                {
                    LoadVersionInfo();
                    LoadAssemblyInfo();
                    LoadCompatibilityWarnings();
                    StatusText.Text = "版本检测完成";
                }
                else
                {
                    StatusText.Text = "版本检测失败";
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"重新检测版本时发生错误: {ex.Message}");
                StatusText.Text = $"检测失败: {ex.Message}";
            }
            finally
            {
                RefreshVersionButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// 保存配置按钮点击事件
        /// </summary>
        private void SaveConfigButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 保存配置
                _config.PreferredTeklaVersion = PreferredVersionComboBox.SelectedItem?.ToString() ?? string.Empty;
                _config.EnableCompatibilityCheck = EnableCompatibilityCheckBox.IsChecked ?? true;
                _config.ShowVersionWarnings = ShowVersionWarningsCheckBox.IsChecked ?? true;
                _config.EnableAssemblyCache = EnableAssemblyCacheCheckBox.IsChecked ?? true;
                _config.ForceGacLoading = ForceGacLoadingCheckBox.IsChecked ?? true;

                if (int.TryParse(TimeoutTextBox.Text, out int timeout))
                {
                    _config.AssemblyLoadTimeoutSeconds = timeout;
                }

                if (int.TryParse(CacheHoursTextBox.Text, out int cacheHours))
                {
                    _config.DetectionCacheHours = cacheHours;
                }

                _config.SaveConfig();
                StatusText.Text = "配置已保存";

                MessageBox.Show("配置已保存成功！\n\n某些设置可能需要重启程序后生效。",
                               "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Logger.LogError($"保存配置时发生错误: {ex.Message}");
                MessageBox.Show($"保存配置时发生错误:\n{ex.Message}",
                               "保存失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 重置配置按钮点击事件
        /// </summary>
        private void ResetConfigButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有配置为默认值吗？",
                                        "确认重置", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                _config.ResetToDefault();
                LoadConfigInfo();
                StatusText.Text = "配置已重置为默认值";
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    /// <summary>
    /// 程序集信息类
    /// </summary>
    public class TeklaAssemblyInfo
    {
        public string Name { get; set; }
        public string Version { get; set; }
        public string Status { get; set; }
        public string Location { get; set; }
    }
}
