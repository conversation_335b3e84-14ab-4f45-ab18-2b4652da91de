﻿2025-05-27 20:05:18 [INFO] 日志功能已开启
2025-05-27 20:05:20 [INFO] 使用过滤器获取所有零件，包括直梁、折梁和钢板...
2025-05-27 20:05:20 [INFO] 开始处理直梁...
2025-05-27 20:05:31 [INFO] 处理了 405 个对象，耗时 11.6 秒
2025-05-27 20:05:31 [INFO] 开始处理折梁...
2025-05-27 20:05:36 [INFO] 处理了 244 个对象，耗时 4.5 秒
2025-05-27 20:05:36 [INFO] 开始处理钢板...
2025-05-27 20:05:38 [INFO] 处理了 77 个对象，耗时 2.2 秒
2025-05-27 20:05:38 [INFO] 获取了 726 个零件信息，处理了 726 个对象，耗时 18.3 秒
2025-05-27 20:05:38 [INFO] 已更新零件缓存，共 726 个零件 (useCache: True)
2025-05-27 20:05:38 [INFO] 设置零件数据，共 726 个零件
2025-05-27 20:05:38 [INFO] 成功加载 726 个零件
2025-05-27 20:05:50 [INFO] 开始执行刷新数据命令
2025-05-27 20:05:50 [INFO] 已清理所有缓存（零件缓存、构件缓存、构件零件ID缓存）
2025-05-27 20:05:51 [INFO] 设置零件数据，共 0 个零件
2025-05-27 20:05:51 [INFO] 强制重新获取零件数据（忽略缓存）
2025-05-27 20:05:51 [INFO] 使用过滤器获取所有零件，包括直梁、折梁和钢板...
2025-05-27 20:05:51 [INFO] 开始处理直梁...
2025-05-27 20:06:00 [INFO] 处理了 399 个对象，耗时 9.9 秒
2025-05-27 20:06:00 [INFO] 开始处理折梁...
2025-05-27 20:06:05 [INFO] 处理了 244 个对象，耗时 4.1 秒
2025-05-27 20:06:05 [INFO] 开始处理钢板...
2025-05-27 20:06:07 [INFO] 处理了 75 个对象，耗时 2.0 秒
2025-05-27 20:06:07 [INFO] 获取了 718 个零件信息，处理了 718 个对象，耗时 16.1 秒
2025-05-27 20:06:07 [INFO] 已更新零件缓存，共 718 个零件 (useCache: False)
2025-05-27 20:06:07 [INFO] 设置零件数据，共 718 个零件
2025-05-27 20:06:07 [INFO] 成功加载 718 个零件
2025-05-27 20:06:07 [INFO] 数据刷新完成
