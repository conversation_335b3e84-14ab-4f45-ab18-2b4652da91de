<Window x:Class="TeklaTool.Views.TeklaVersionInfoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Tekla版本信息" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="Tekla Structures版本信息" 
                   FontSize="16" FontWeight="Bold" 
                   Margin="0,0,0,15"/>

        <!-- 主要内容区域 -->
        <TabControl Grid.Row="1" Margin="0,0,0,15">
            <!-- 版本信息标签页 -->
            <TabItem Header="版本信息">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 检测到的版本 -->
                        <GroupBox Header="检测到的版本" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="当前版本:" VerticalAlignment="Center"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="CurrentVersionText" 
                                          FontWeight="Bold" VerticalAlignment="Center"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="兼容性状态:" VerticalAlignment="Center" Margin="0,5,0,0"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="CompatibilityStatusText" 
                                          VerticalAlignment="Center" Margin="0,5,0,0"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="检测时间:" VerticalAlignment="Center" Margin="0,5,0,0"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="DetectionTimeText" 
                                          VerticalAlignment="Center" Margin="0,5,0,0"/>

                                <Button Grid.Row="3" Grid.Column="1" x:Name="RefreshVersionButton" 
                                       Content="重新检测版本" Width="100" HorizontalAlignment="Left" 
                                       Margin="0,10,0,0" Click="RefreshVersionButton_Click"/>
                            </Grid>
                        </GroupBox>

                        <!-- 程序集信息 -->
                        <GroupBox Header="已加载的程序集" Margin="0,0,0,15">
                            <DataGrid x:Name="AssembliesDataGrid" 
                                     AutoGenerateColumns="False" 
                                     IsReadOnly="True"
                                     GridLinesVisibility="Horizontal"
                                     HeadersVisibility="Column"
                                     Margin="10"
                                     MaxHeight="200">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="程序集名称" Binding="{Binding Name}" Width="200"/>
                                    <DataGridTextColumn Header="版本" Binding="{Binding Version}" Width="100"/>
                                    <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                                    <DataGridTextColumn Header="位置" Binding="{Binding Location}" Width="*"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </GroupBox>

                        <!-- 兼容性警告 -->
                        <GroupBox Header="兼容性信息" x:Name="WarningsGroupBox">
                            <ListBox x:Name="WarningsListBox" 
                                    MaxHeight="150"
                                    ScrollViewer.VerticalScrollBarVisibility="Auto"
                                    Margin="10">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="⚠" Foreground="Orange" Margin="0,0,5,0"/>
                                            <TextBlock Text="{Binding}" TextWrapping="Wrap"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 配置标签页 -->
            <TabItem Header="配置选项">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 版本偏好设置 -->
                        <GroupBox Header="版本偏好设置" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="首选版本:" VerticalAlignment="Center"/>
                                <ComboBox Grid.Row="0" Grid.Column="1" x:Name="PreferredVersionComboBox" 
                                         IsEditable="True" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="说明:" VerticalAlignment="Top" Margin="0,5,0,0"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" 
                                          Text="留空表示自动检测。指定版本后程序将优先使用该版本。" 
                                          TextWrapping="Wrap" Margin="0,5,0,0" 
                                          Foreground="Gray" FontSize="11"/>
                            </Grid>
                        </GroupBox>

                        <!-- 高级选项 -->
                        <GroupBox Header="高级选项" Margin="0,0,0,15">
                            <StackPanel Margin="10">
                                <CheckBox x:Name="EnableCompatibilityCheckBox" 
                                         Content="启用版本兼容性检查" Margin="0,0,0,5"/>
                                <CheckBox x:Name="ShowVersionWarningsCheckBox" 
                                         Content="显示版本警告信息" Margin="0,0,0,5"/>
                                <CheckBox x:Name="EnableAssemblyCacheCheckBox" 
                                         Content="启用程序集缓存" Margin="0,0,0,5"/>
                                <CheckBox x:Name="ForceGacLoadingCheckBox" 
                                         Content="强制从GAC加载程序集" Margin="0,0,0,10"/>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="100"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="加载超时(秒):" VerticalAlignment="Center"/>
                                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="TimeoutTextBox" 
                                            Text="30" Margin="0,0,10,5"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="缓存有效期(小时):" VerticalAlignment="Center" Margin="0,5,0,0"/>
                                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="CacheHoursTextBox" 
                                            Text="24" Margin="0,5,10,0"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                        <!-- 操作按钮 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button x:Name="ResetConfigButton" Content="重置为默认" 
                                   Width="100" Margin="0,0,10,0" Click="ResetConfigButton_Click"/>
                            <Button x:Name="SaveConfigButton" Content="保存配置" 
                                   Width="80" Click="SaveConfigButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 支持的版本标签页 -->
            <TabItem Header="支持的版本">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <TextBlock Text="本程序支持以下Tekla Structures版本:" 
                                  FontWeight="Bold" Margin="0,0,0,10"/>
                        <ListBox x:Name="SupportedVersionsListBox" 
                                MaxHeight="300"
                                ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="✓" Foreground="Green" Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="LightGray" Padding="5" Margin="0,0,0,10">
            <TextBlock x:Name="StatusText" Text="就绪" FontSize="11"/>
        </Border>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button x:Name="OkButton" Content="确定" Width="80" Margin="0,0,10,0" 
                   IsDefault="True" Click="OkButton_Click"/>
            <Button x:Name="CancelButton" Content="取消" Width="80" 
                   IsCancel="True" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
