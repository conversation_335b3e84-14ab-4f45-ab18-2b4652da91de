﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <RootNamespace>TeklaTool</RootNamespace>
    <AssemblyName>TeklaListWPF</AssemblyName>
    <LangVersion>8.0</LangVersion>
    <Version>1.2.0.0</Version>
    <AssemblyVersion>1.2.0.0</AssemblyVersion>
    <FileVersion>1.2.0.0</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <!-- Tekla Structures程序集引用 - 支持多版本兼容 -->
    <!-- 编译时回退到原路径，运行时使用动态加载 -->
    <Reference Include="Tekla.Structures" Condition="Exists('..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.dll')">
      <HintPath>..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Tekla.Structures" Condition="!Exists('..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.dll')">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>

    <Reference Include="Tekla.Structures.Drawing" Condition="Exists('..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Drawing.dll')">
      <HintPath>..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Tekla.Structures.Drawing" Condition="!Exists('..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Drawing.dll')">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>

    <Reference Include="Tekla.Structures.Model" Condition="Exists('..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Model.dll')">
      <HintPath>..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Model.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Tekla.Structures.Model" Condition="!Exists('..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Model.dll')">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Utils\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
  </ItemGroup>

</Project>
