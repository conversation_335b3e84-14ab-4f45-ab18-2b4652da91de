# Tekla Structures 版本兼容性说明

## 概述

本程序现在支持自动适配不同版本的Tekla Structures，无需手动修改引用路径。程序会自动从GAC（全局程序集缓存）中加载用户系统上安装的Tekla版本对应的DLL。

## 支持的版本

程序支持以下Tekla Structures版本：

- Tekla Structures 19.0 / 19.1
- Tekla Structures 20.0 / 20.1  
- Tekla Structures 21.0 / 21.1
- Tekla Structures 2019.0 / 2019.1
- Tekla Structures 2020.0 / 2020.1
- Tekla Structures 2021.0 / 2021.1
- Tekla Structures 2022.0
- Tekla Structures 2023.0

## 工作原理

### 1. 自动版本检测
程序启动时会自动检测系统中安装的Tekla版本，检测方法包括：
- 从Windows注册表检测
- 从GAC（全局程序集缓存）检测
- 从常见安装路径检测

### 2. 动态程序集加载
- 程序不再使用固定路径引用Tekla DLL
- 改为从GAC中动态加载对应版本的程序集
- 支持多版本共存的环境

### 3. 版本兼容性检查
- 自动检查检测到的版本是否受支持
- 提供兼容性警告和建议
- 记录版本信息用于问题诊断

## 使用说明

### 首次使用
1. 确保已正确安装Tekla Structures
2. 启动程序，系统会自动检测Tekla版本
3. 如果检测成功，程序会显示检测到的版本信息
4. 如果检测失败，程序会提示用户确认是否继续

### 查看版本信息
1. 在主窗口菜单栏选择"工具" → "Tekla版本信息"
2. 在弹出的窗口中可以查看：
   - 当前检测到的Tekla版本
   - 兼容性状态
   - 已加载的程序集信息
   - 兼容性警告（如有）

### 配置选项
在版本信息窗口的"配置选项"标签页中，可以设置：

#### 版本偏好设置
- **首选版本**：可以指定优先使用的Tekla版本（留空表示自动检测）

#### 高级选项
- **启用版本兼容性检查**：是否进行版本兼容性验证
- **显示版本警告信息**：是否显示兼容性警告
- **启用程序集缓存**：是否缓存已加载的程序集
- **强制从GAC加载程序集**：是否强制从GAC加载（推荐）
- **加载超时时间**：程序集加载的超时时间（秒）
- **缓存有效期**：版本检测结果的缓存时间（小时）

## 故障排除

### 问题1：程序启动时提示"无法检测到Tekla版本"
**可能原因：**
- Tekla Structures未正确安装
- Tekla程序集未注册到GAC
- 安装的版本不在支持列表中

**解决方法：**
1. 确认Tekla Structures已正确安装并能正常运行
2. 尝试重新安装Tekla Structures
3. 检查安装的版本是否在支持列表中
4. 在版本信息窗口中点击"重新检测版本"

### 问题2：程序运行时出现兼容性警告
**说明：**
这通常不会影响程序的基本功能，但可能存在一些API差异。

**解决方法：**
1. 查看具体的警告信息
2. 如果功能正常，可以忽略警告
3. 如果遇到问题，请联系开发者

### 问题3：程序无法连接到Tekla模型
**可能原因：**
- Tekla Structures未启动
- 未打开模型
- 版本不兼容

**解决方法：**
1. 确保Tekla Structures已启动并打开了模型
2. 检查版本兼容性
3. 尝试重启程序

## 技术细节

### 程序集引用方式
```xml
<!-- 旧方式：固定路径引用 -->
<Reference Include="Tekla.Structures">
  <HintPath>..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.dll</HintPath>
</Reference>

<!-- 新方式：GAC引用 -->
<Reference Include="Tekla.Structures">
  <SpecificVersion>False</SpecificVersion>
</Reference>
```

### 配置文件位置
配置文件保存在：
```
%APPDATA%\TeklaListWPF\tekla-config.json
```

### 日志文件
程序运行日志包含版本检测和加载信息，可用于问题诊断。

## 开发者信息

### 新增的类和文件
- `Utils/TeklaAssemblyLoader.cs` - 动态程序集加载器
- `Utils/TeklaVersionCompatibility.cs` - 版本兼容性管理器
- `Utils/TeklaConfig.cs` - Tekla配置管理器
- `Views/TeklaVersionInfoWindow.xaml` - 版本信息窗口

### 主要修改
- 修改了项目文件中的程序集引用方式
- 在应用程序启动时初始化程序集加载器
- 在TeklaModelService中添加了版本检查
- 在主窗口中添加了版本信息菜单

## 更新历史

### v1.2.0
- 添加了Tekla版本自动检测功能
- 实现了动态程序集加载
- 添加了版本兼容性检查
- 提供了版本信息和配置界面

---

如有问题或建议，请联系开发者。
