﻿#pragma checksum "..\..\..\..\Views\TeklaVersionInfoWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8D1E7BD94466A7A1E9F266013C9079810F12EDAC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace TeklaTool.Views {
    
    
    /// <summary>
    /// TeklaVersionInfoWindow
    /// </summary>
    public partial class TeklaVersionInfoWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 41 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentVersionText;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompatibilityStatusText;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DetectionTimeText;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshVersionButton;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid AssembliesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox WarningsGroupBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox WarningsListBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PreferredVersionComboBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableCompatibilityCheckBox;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowVersionWarningsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAssemblyCacheCheckBox;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ForceGacLoadingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CacheHoursTextBox;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetConfigButton;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveConfigButton;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SupportedVersionsListBox;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OkButton;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TeklaListWPF;V1.2.0.0;component/views/teklaversioninfowindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CurrentVersionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CompatibilityStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.DetectionTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.RefreshVersionButton = ((System.Windows.Controls.Button)(target));
            
            #line 54 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
            this.RefreshVersionButton.Click += new System.Windows.RoutedEventHandler(this.RefreshVersionButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AssembliesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 6:
            this.WarningsGroupBox = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 7:
            this.WarningsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 8:
            this.PreferredVersionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.EnableCompatibilityCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.ShowVersionWarningsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.EnableAssemblyCacheCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.ForceGacLoadingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.TimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.CacheHoursTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ResetConfigButton = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
            this.ResetConfigButton.Click += new System.Windows.RoutedEventHandler(this.ResetConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.SaveConfigButton = ((System.Windows.Controls.Button)(target));
            
            #line 164 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
            this.SaveConfigButton.Click += new System.Windows.RoutedEventHandler(this.SaveConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SupportedVersionsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 18:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.OkButton = ((System.Windows.Controls.Button)(target));
            
            #line 201 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
            this.OkButton.Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 203 "..\..\..\..\Views\TeklaVersionInfoWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

