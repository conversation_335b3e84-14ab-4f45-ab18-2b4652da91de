using System;
using System.IO;
using Newtonsoft.Json;

namespace TeklaTool.Utils
{
    /// <summary>
    /// Tekla配置管理器
    /// 管理Tekla相关的配置信息，包括版本偏好、程序集路径等
    /// </summary>
    public class TeklaConfig
    {
        private static TeklaConfig _instance;
        private static readonly object _lock = new object();
        private readonly string _configFilePath;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static TeklaConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new TeklaConfig();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 首选的Tekla版本（如果为空则自动检测）
        /// </summary>
        public string PreferredTeklaVersion { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用版本兼容性检查
        /// </summary>
        public bool EnableCompatibilityCheck { get; set; } = true;

        /// <summary>
        /// 是否显示版本警告
        /// </summary>
        public bool ShowVersionWarnings { get; set; } = true;

        /// <summary>
        /// 程序集加载超时时间（秒）
        /// </summary>
        public int AssemblyLoadTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 是否启用程序集缓存
        /// </summary>
        public bool EnableAssemblyCache { get; set; } = true;

        /// <summary>
        /// 自定义程序集路径（用于特殊情况）
        /// </summary>
        public string CustomAssemblyPath { get; set; } = string.Empty;

        /// <summary>
        /// 是否强制使用GAC加载
        /// </summary>
        public bool ForceGacLoading { get; set; } = true;

        /// <summary>
        /// 最后检测到的Tekla版本
        /// </summary>
        public string LastDetectedVersion { get; set; } = string.Empty;

        /// <summary>
        /// 最后检测时间
        /// </summary>
        public DateTime LastDetectionTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 版本检测缓存有效期（小时）
        /// </summary>
        public int DetectionCacheHours { get; set; } = 24;

        private TeklaConfig()
        {
            _configFilePath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "TeklaListWPF",
                "tekla-config.json");

            LoadConfig();
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfig()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = File.ReadAllText(_configFilePath);
                    var config = JsonConvert.DeserializeObject<TeklaConfig>(json);
                    
                    if (config != null)
                    {
                        // 复制属性值
                        PreferredTeklaVersion = config.PreferredTeklaVersion ?? string.Empty;
                        EnableCompatibilityCheck = config.EnableCompatibilityCheck;
                        ShowVersionWarnings = config.ShowVersionWarnings;
                        AssemblyLoadTimeoutSeconds = config.AssemblyLoadTimeoutSeconds;
                        EnableAssemblyCache = config.EnableAssemblyCache;
                        CustomAssemblyPath = config.CustomAssemblyPath ?? string.Empty;
                        ForceGacLoading = config.ForceGacLoading;
                        LastDetectedVersion = config.LastDetectedVersion ?? string.Empty;
                        LastDetectionTime = config.LastDetectionTime;
                        DetectionCacheHours = config.DetectionCacheHours;

                        Logger.LogInfo("已加载Tekla配置文件");
                    }
                }
                else
                {
                    Logger.LogInfo("配置文件不存在，使用默认配置");
                    SaveConfig(); // 创建默认配置文件
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"加载Tekla配置时发生错误: {ex.Message}，使用默认配置");
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfig()
        {
            try
            {
                var directory = Path.GetDirectoryName(_configFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(_configFilePath, json);
                
                Logger.LogInfo("已保存Tekla配置文件");
            }
            catch (Exception ex)
            {
                Logger.LogError($"保存Tekla配置时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public void ResetToDefault()
        {
            PreferredTeklaVersion = string.Empty;
            EnableCompatibilityCheck = true;
            ShowVersionWarnings = true;
            AssemblyLoadTimeoutSeconds = 30;
            EnableAssemblyCache = true;
            CustomAssemblyPath = string.Empty;
            ForceGacLoading = true;
            LastDetectedVersion = string.Empty;
            LastDetectionTime = DateTime.MinValue;
            DetectionCacheHours = 24;

            SaveConfig();
            Logger.LogInfo("已重置Tekla配置为默认值");
        }

        /// <summary>
        /// 更新最后检测到的版本信息
        /// </summary>
        public void UpdateLastDetectedVersion(string version)
        {
            if (!string.IsNullOrEmpty(version))
            {
                LastDetectedVersion = version;
                LastDetectionTime = DateTime.Now;
                SaveConfig();
            }
        }

        /// <summary>
        /// 检查版本检测缓存是否有效
        /// </summary>
        public bool IsDetectionCacheValid()
        {
            return !string.IsNullOrEmpty(LastDetectedVersion) &&
                   LastDetectionTime.AddHours(DetectionCacheHours) > DateTime.Now;
        }

        /// <summary>
        /// 获取有效的Tekla版本（优先级：首选版本 > 缓存版本 > 自动检测）
        /// </summary>
        public string GetEffectiveTeklaVersion()
        {
            // 1. 如果设置了首选版本，优先使用
            if (!string.IsNullOrEmpty(PreferredTeklaVersion))
            {
                return PreferredTeklaVersion;
            }

            // 2. 如果缓存有效，使用缓存的版本
            if (IsDetectionCacheValid())
            {
                return LastDetectedVersion;
            }

            // 3. 返回空字符串，表示需要重新检测
            return string.Empty;
        }

        /// <summary>
        /// 验证配置的有效性
        /// </summary>
        public bool ValidateConfig()
        {
            var isValid = true;

            if (AssemblyLoadTimeoutSeconds <= 0 || AssemblyLoadTimeoutSeconds > 300)
            {
                Logger.LogWarning("程序集加载超时时间设置无效，已重置为30秒");
                AssemblyLoadTimeoutSeconds = 30;
                isValid = false;
            }

            if (DetectionCacheHours <= 0 || DetectionCacheHours > 168) // 最多7天
            {
                Logger.LogWarning("版本检测缓存时间设置无效，已重置为24小时");
                DetectionCacheHours = 24;
                isValid = false;
            }

            if (!string.IsNullOrEmpty(CustomAssemblyPath) && !Directory.Exists(CustomAssemblyPath))
            {
                Logger.LogWarning($"自定义程序集路径不存在: {CustomAssemblyPath}");
                isValid = false;
            }

            if (!isValid)
            {
                SaveConfig();
            }

            return isValid;
        }
    }
}
