using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Linq;
using Microsoft.Win32;

namespace TeklaTool.Utils
{
    /// <summary>
    /// Tekla Structures程序集动态加载器
    /// 支持从GAC中自动加载用户系统上安装的Tekla版本对应的DLL
    /// </summary>
    public static class TeklaAssemblyLoader
    {
        private static readonly Dictionary<string, Assembly> _loadedAssemblies = new Dictionary<string, Assembly>();
        private static string _detectedTeklaVersion = null;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 检测到的Tekla版本
        /// </summary>
        public static string DetectedTeklaVersion => _detectedTeklaVersion;

        /// <summary>
        /// 初始化Tekla程序集加载器
        /// </summary>
        public static bool Initialize()
        {
            lock (_lockObject)
            {
                try
                {
                    Logger.LogInfo("开始初始化Tekla程序集加载器...");

                    var config = TeklaConfig.Instance;
                    config.ValidateConfig();

                    // 优先使用配置中的有效版本
                    var effectiveVersion = config.GetEffectiveTeklaVersion();
                    if (!string.IsNullOrEmpty(effectiveVersion))
                    {
                        Logger.LogInfo($"使用配置中的Tekla版本: {effectiveVersion}");
                        _detectedTeklaVersion = effectiveVersion;
                    }
                    else
                    {
                        // 检测系统中安装的Tekla版本
                        _detectedTeklaVersion = DetectTeklaVersion();

                        if (string.IsNullOrEmpty(_detectedTeklaVersion))
                        {
                            Logger.LogError("未检测到系统中安装的Tekla Structures版本");
                            return false;
                        }

                        // 更新配置中的检测结果
                        config.UpdateLastDetectedVersion(_detectedTeklaVersion);
                        Logger.LogInfo($"检测到Tekla Structures版本: {_detectedTeklaVersion}");
                    }

                    // 预加载核心程序集
                    var coreAssemblies = new[]
                    {
                        "Tekla.Structures",
                        "Tekla.Structures.Model",
                        "Tekla.Structures.Drawing"
                    };

                    bool allLoaded = true;
                    foreach (var assemblyName in coreAssemblies)
                    {
                        var assembly = LoadAssembly(assemblyName);
                        if (assembly == null)
                        {
                            Logger.LogWarning($"无法加载程序集: {assemblyName}");
                            allLoaded = false;
                        }
                        else
                        {
                            Logger.LogInfo($"成功加载程序集: {assemblyName} (版本: {assembly.GetName().Version})");
                        }
                    }

                    if (allLoaded)
                    {
                        Logger.LogInfo("Tekla程序集加载器初始化完成，所有核心程序集已加载");
                    }
                    else
                    {
                        Logger.LogWarning("Tekla程序集加载器初始化完成，但部分程序集加载失败");
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    Logger.LogError($"初始化Tekla程序集加载器时发生错误: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// 检测系统中安装的Tekla Structures版本
        /// </summary>
        private static string DetectTeklaVersion()
        {
            try
            {
                // 方法1: 从注册表检测
                var version = DetectFromRegistry();
                if (!string.IsNullOrEmpty(version))
                {
                    Logger.LogInfo($"从注册表检测到Tekla版本: {version}");
                    return version;
                }

                // 方法2: 从GAC检测
                version = DetectFromGAC();
                if (!string.IsNullOrEmpty(version))
                {
                    Logger.LogInfo($"从GAC检测到Tekla版本: {version}");
                    return version;
                }

                // 方法3: 从常见安装路径检测
                version = DetectFromInstallPath();
                if (!string.IsNullOrEmpty(version))
                {
                    Logger.LogInfo($"从安装路径检测到Tekla版本: {version}");
                    return version;
                }

                Logger.LogWarning("所有检测方法都未能找到Tekla版本");
                return null;
            }
            catch (Exception ex)
            {
                Logger.LogError($"检测Tekla版本时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从注册表检测Tekla版本
        /// </summary>
        private static string DetectFromRegistry()
        {
            try
            {
                // 检查常见的注册表路径
                var registryPaths = new[]
                {
                    @"SOFTWARE\Tekla\Tekla Structures",
                    @"SOFTWARE\WOW6432Node\Tekla\Tekla Structures"
                };

                foreach (var path in registryPaths)
                {
                    using (var key = Registry.LocalMachine.OpenSubKey(path))
                    {
                        if (key != null)
                        {
                            var subKeyNames = key.GetSubKeyNames();
                            // 查找最新版本
                            var versions = subKeyNames
                                .Where(name => IsValidVersionString(name))
                                .OrderByDescending(v => new Version(v))
                                .ToList();

                            if (versions.Any())
                            {
                                return versions.First();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogWarning($"从注册表检测Tekla版本时发生错误: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 从GAC检测Tekla版本
        /// </summary>
        private static string DetectFromGAC()
        {
            try
            {
                // 尝试加载不同版本的Tekla.Structures程序集
                var possibleVersions = new[]
                {
                    "2023.0", "2022.0", "2021.1", "2021.0", "2020.1", "2020.0",
                    "2019.1", "2019.0", "21.1", "21.0", "20.1", "20.0", "19.1", "19.0"
                };

                foreach (var version in possibleVersions)
                {
                    try
                    {
                        var assemblyName = $"Tekla.Structures, Version={version}.0.0, Culture=neutral, PublicKeyToken=2f04dbe497b71114";
                        var assembly = Assembly.Load(assemblyName);
                        if (assembly != null)
                        {
                            return version;
                        }
                    }
                    catch
                    {
                        // 继续尝试下一个版本
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogWarning($"从GAC检测Tekla版本时发生错误: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 从安装路径检测Tekla版本
        /// </summary>
        private static string DetectFromInstallPath()
        {
            try
            {
                var commonPaths = new[]
                {
                    @"C:\TeklaStructures",
                    @"C:\Program Files\Tekla Structures",
                    @"C:\Program Files (x86)\Tekla Structures"
                };

                foreach (var basePath in commonPaths)
                {
                    if (Directory.Exists(basePath))
                    {
                        var versionDirs = Directory.GetDirectories(basePath)
                            .Select(Path.GetFileName)
                            .Where(IsValidVersionString)
                            .OrderByDescending(v => new Version(v))
                            .ToList();

                        if (versionDirs.Any())
                        {
                            return versionDirs.First();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogWarning($"从安装路径检测Tekla版本时发生错误: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 验证版本字符串是否有效
        /// </summary>
        private static bool IsValidVersionString(string version)
        {
            if (string.IsNullOrEmpty(version)) return false;

            // 支持格式: 19.0, 20.0, 21.0, 2019.0, 2020.0等
            return Version.TryParse(version, out _) ||
                   (version.Contains(".") && version.Split('.').All(part => int.TryParse(part, out _)));
        }

        /// <summary>
        /// 动态加载指定的Tekla程序集
        /// </summary>
        public static Assembly LoadAssembly(string assemblyName)
        {
            lock (_lockObject)
            {
                if (_loadedAssemblies.ContainsKey(assemblyName))
                {
                    return _loadedAssemblies[assemblyName];
                }

                try
                {
                    Assembly assembly = null;

                    // 方法1: 尝试从GAC加载（推荐方式）
                    try
                    {
                        assembly = Assembly.Load(assemblyName);
                        Logger.LogInfo($"从GAC成功加载程序集: {assemblyName}");
                    }
                    catch
                    {
                        // 如果简单加载失败，尝试带版本信息的加载
                        if (!string.IsNullOrEmpty(_detectedTeklaVersion))
                        {
                            var fullAssemblyName = $"{assemblyName}, Version={_detectedTeklaVersion}.0.0, Culture=neutral, PublicKeyToken=2f04dbe497b71114";
                            assembly = Assembly.Load(fullAssemblyName);
                            Logger.LogInfo($"使用完整名称从GAC加载程序集: {fullAssemblyName}");
                        }
                    }

                    if (assembly != null)
                    {
                        _loadedAssemblies[assemblyName] = assembly;
                        return assembly;
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError($"加载程序集 {assemblyName} 时发生错误: {ex.Message}");
                }

                return null;
            }
        }

        /// <summary>
        /// 获取已加载的程序集
        /// </summary>
        public static Assembly GetLoadedAssembly(string assemblyName)
        {
            lock (_lockObject)
            {
                return _loadedAssemblies.ContainsKey(assemblyName) ? _loadedAssemblies[assemblyName] : null;
            }
        }

        /// <summary>
        /// 清理已加载的程序集缓存
        /// </summary>
        public static void ClearCache()
        {
            lock (_lockObject)
            {
                _loadedAssemblies.Clear();
                Logger.LogInfo("已清理Tekla程序集缓存");
            }
        }
    }
}
